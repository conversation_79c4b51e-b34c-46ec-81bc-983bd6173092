Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBF80, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 0007FFFFBF80, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCA0D80000 ntdll.dll
7FFC9ECE0000 KERNEL32.DLL
7FFC9E1F0000 KERNELBASE.dll
7FFC9FE50000 USER32.dll
7FFC9DF80000 win32u.dll
7FFC9EB20000 GDI32.dll
7FFC9E5E0000 gdi32full.dll
7FFC9DED0000 msvcp_win.dll
7FFC9E7C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCA0C40000 advapi32.dll
7FFC9EBE0000 msvcrt.dll
7FFCA05D0000 sechost.dll
7FFCA0B20000 RPCRT4.dll
7FFC9D3E0000 CRYPTBASE.DLL
7FFC9E720000 bcryptPrimitives.dll
7FFCA0D00000 IMM32.DLL
